import React, { useState, useCallback } from 'react';
import { View, ScrollView, Pressable } from 'react-native';
import { Icon } from '@/components/ui/icon';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { PlusIcon, XIcon, UserPlusIcon } from 'lucide-react-native';
import { type Team } from '@/types/teams';
import { type Tournament } from '@/pages/TournamentViewScreen/types';
import { type Player } from '@/types/player';
import { useFocusEffect, useRouter } from 'expo-router';
import { useEffect } from 'react';
import { fetchPlayers, updatePlayer } from '@/services/playerService';
import NoDataFound from '@/components/k-components/NoDataFound';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { Spinner } from '@/components/ui/spinner';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
} from '@/components/ui/actionsheet';
import { ActionsheetDragIndicator } from '@/components/ui/select/select-actionsheet';
import AddPlayer from '../Players/AddPlayer';
import {
  AsyncSelectWithSearch,
  type Option,
} from '@/components/k-components/AsyncSelectWithSearch';
import { toast } from '@/toast/toast';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import SCREENS from '@/constants/Screens';
import LogoImage from '@/components/k-components/LogoImage';

interface SquadPageProps {
  team: Team;
  tournament: Tournament;
  mode: 'create' | 'edit';
}

const SquadPage: React.FC<SquadPageProps> = ({ team, tournament, mode }) => {
  const router = useRouter();
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [showAddPlayer, setShowAddPlayer] = useState(false);
  const [showSelectPlayer, setShowSelectPlayer] = useState(false);
  const [availablePlayers, setAvailablePlayers] = useState<Option[]>([]);
  const [hasMorePlayers, setHasMorePlayers] = useState(false);
  const [loadingPlayers, setLoadingPlayers] = useState(false);
  const [playerOperations, setPlayerOperations] = useState<Set<string>>(
    new Set()
  );

  const loadPlayers = useCallback(async () => {
    setLoading(true);
    try {
      const {
        players: fetchedPlayers,
        count,
        error,
      } = await fetchPlayers({
        tournamentId: tournament.id,
        teamId: team.id,
        limit: 50, // Load all players for squad view
      });

      if (error) {
        toast.error('Failed to load squad players');
        return;
      }

      setPlayers(fetchedPlayers || []);
      setTotalCount(count || 0);
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  }, [team.id, tournament.id]);

  useFocusEffect(
    useCallback(() => {
      loadPlayers();
    }, [loadPlayers])
  );

  // Load available players for selection (exclude players already on teams)
  const loadAvailablePlayers = useCallback(
    async (searchQuery: string, page: number) => {
      setLoadingPlayers(true);
      try {
        const {
          players: fetchedPlayers,
          count,
          error,
        } = await fetchPlayers({
          tournamentId: tournament.id,
          search: searchQuery,
          page,
          limit: 20,
        });

        if (error) {
          toast.error('Failed to load available players');
          return;
        }

        // Filter out players who already have a team_id
        const unassignedPlayers = (fetchedPlayers || []).filter(
          (player) => !player.team_id
        );

        const playerOptions: Option[] = unassignedPlayers.map((player) => ({
          label: player.name,
          value: player.id,
          player,
        }));

        if (page === 1) {
          setAvailablePlayers(playerOptions);
        } else {
          setAvailablePlayers((prev) => [...prev, ...playerOptions]);
        }

        setHasMorePlayers((count || 0) > page * 20);
      } catch (error) {
        console.error('Error loading available players:', error);
        toast.error('Something went wrong');
      } finally {
        setLoadingPlayers(false);
      }
    },
    [tournament.id]
  );

  // Load available players when select modal opens
  useEffect(() => {
    if (showSelectPlayer) {
      console.log('Loading available players for selection...');
      loadAvailablePlayers('', 1);
    } else {
      // Reset when modal closes
      setAvailablePlayers([]);
      setHasMorePlayers(false);
    }
  }, [showSelectPlayer, loadAvailablePlayers]);

  // Handle search change for available players
  const handleSearchChange = useCallback(
    (query: string) => {
      loadAvailablePlayers(query, 1);
    },
    [loadAvailablePlayers]
  );

  // Handle final player selection (called when Continue is pressed)
  const handlePlayerSelect = useCallback(
    async (selected: Option | Option[] | null) => {
      if (!selected) return;

      const selectedPlayers = Array.isArray(selected) ? selected : [selected];
      if (selectedPlayers.length === 0) return;

      console.log('Confirming player selections:', selectedPlayers);

      // Add all selected players to operations set
      const playerIds = selectedPlayers.map((p) => p.value);
      setPlayerOperations((prev) => {
        const newSet = new Set(prev);
        playerIds.forEach((id) => newSet.add(id));
        return newSet;
      });

      try {
        // Update all selected players in parallel
        const updatePromises = selectedPlayers.map(async (playerOption) => {
          const { success, error } = await updatePlayer(playerOption.value, {
            team_id: team.id,
          });

          if (!success) {
            throw new Error(
              error || `Failed to add ${playerOption.label} to squad`
            );
          }

          return playerOption.label;
        });

        const addedPlayerNames = await Promise.all(updatePromises);

        setShowSelectPlayer(false);
        loadPlayers(); // Refresh the list

        if (addedPlayerNames.length === 1) {
          toast.success(`${addedPlayerNames[0]} added to squad successfully`);
        } else {
          toast.success(
            `${addedPlayerNames.length} players added to squad successfully`
          );
        }
      } catch (error) {
        console.error('Error adding players to squad:', error);
        toast.error(
          error instanceof Error
            ? error.message
            : 'Failed to add some players to squad'
        );
      } finally {
        // Remove all player IDs from operations set
        setPlayerOperations((prev) => {
          const newSet = new Set(prev);
          playerIds.forEach((id) => newSet.delete(id));
          return newSet;
        });
      }
    },
    [team.id, loadPlayers]
  );

  // Handle removing player from squad
  const handleRemovePlayer = useCallback(
    async (playerId: string) => {
      setPlayerOperations((prev) => new Set(prev).add(playerId));

      try {
        const { success, error } = await updatePlayer(playerId, {
          team_id: null,
        });

        if (!success) {
          toast.error(error || 'Failed to remove player from squad');
          return;
        }

        loadPlayers(); // Refresh the list
        toast.success('Player removed from squad');
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        setPlayerOperations((prev) => {
          const newSet = new Set(prev);
          newSet.delete(playerId);
          return newSet;
        });
      }
    },
    [loadPlayers]
  );

  const handlePlayerAdded = () => {
    setShowAddPlayer(false);
    loadPlayers(); // Refresh the list
    toast.success('Player added to squad successfully');
  };

  const handleContinue = () => {
    router.push({
      pathname: SCREENS.TEAM_VIEW,
      params: {
        'team-id': team.id,
        'tournament-id': tournament.id,
      },
    });
  };

  // Custom player card component with remove functionality
  const SquadPlayerCard = ({ player }: { player: Player }) => {
    const isLoading = playerOperations.has(player.id);

    return (
      <View className="flex-row items-center justify-between py-3 px-2 bg-white rounded-lg border-b border-outline-100">
        <HStack className="items-center space-x-3 flex-1">
          <LogoImage
            width={40}
            height={40}
            borderRadius={100}
            fallbackText={player.name}
            fallBacktextClassName={'text-xl font-urbanistBold'}
          />
          <VStack className="flex-1 ml-3">
            <Text className="text-base font-urbanistMedium text-typography-800">
              {player.name}
            </Text>
            {player.jersey_number && (
              <Text className="text-xs text-typography-600 font-urbanist">
                Jersey #{player.jersey_number}
              </Text>
            )}
          </VStack>
        </HStack>

        {isLoading ? (
          <Spinner size="small" className="text-red-500" />
        ) : (
          <Pressable
            onPress={() => handleRemovePlayer(player.id)}
            disabled={isLoading}
            className="w-6 h-6 items-center justify-center rounded-full border border-red-500"
          >
            <Icon as={XIcon} size="sm" className="text-red-500" />
          </Pressable>
        )}
      </View>
    );
  };

  const renderSquadHeader = () => (
    <VStack className="px-4 py-4 space-y-3">
      {totalCount > 0 && (
        <HStack className="items-center justify-between">
          <Text className="text-sm font-urbanistMedium text-typography-700">
            {totalCount} {totalCount === 1 ? 'Player' : 'Players'}
          </Text>
          <HStack className="space-x-2 gap-3">
            <Button
              size="sm"
              variant="outline"
              onPress={() => setShowSelectPlayer(true)}
            >
              <ButtonIcon as={UserPlusIcon} size="sm" />
              <ButtonText className="font-urbanistSemiBold">
                Select Player
              </ButtonText>
            </Button>
            <Button
              size="sm"
              className="bg-primary-0"
              onPress={() => setShowAddPlayer(true)}
            >
              <ButtonIcon as={PlusIcon} size="sm" />
              <ButtonText className="font-urbanistSemiBold">Add New</ButtonText>
            </Button>
          </HStack>
        </HStack>
      )}
    </VStack>
  );

  const renderPlayersList = () => {
    if (loading) {
      return <FullscreenLoader />;
    }

    if (players.length === 0) {
      return (
        <View className="px-4">
          <NoDataFound
            title="No Players in Squad"
            subtitle={
              mode === 'create'
                ? 'Start building your team by adding your first player.'
                : 'Add players to build your squad.'
            }
            action={
              <VStack className="space-y-3 w-full gap-4">
                <Button
                  variant="outline"
                  onPress={() => setShowSelectPlayer(true)}
                >
                  <ButtonIcon as={UserPlusIcon} />
                  <ButtonText className="font-urbanistSemiBold">
                    Select Existing Player
                  </ButtonText>
                </Button>
                <Button
                  className="bg-primary-0"
                  onPress={() => setShowAddPlayer(true)}
                >
                  <ButtonIcon as={PlusIcon} />
                  <ButtonText className="font-urbanistSemiBold">
                    Add New Player
                  </ButtonText>
                </Button>
              </VStack>
            }
          />
        </View>
      );
    }

    return (
      <VStack className="px-4 space-y-3">
        {players.map((player) => (
          <SquadPlayerCard key={player.id} player={player} />
        ))}
      </VStack>
    );
  };

  return (
    <View className="flex-1">
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        {renderSquadHeader()}
        {renderPlayersList()}
      </ScrollView>

      {/* Continue Button - Fixed at bottom */}
      <View className="px-4 py-4 bg-white border-t border-outline-100">
        <CTAButton title="Continue" onPress={handleContinue} />
      </View>

      {/* Add New Player Modal */}
      <Actionsheet
        isOpen={showAddPlayer}
        onClose={() => setShowAddPlayer(false)}
      >
        <ActionsheetBackdrop />
        <ActionsheetContent className="max-h-[90%]">
          <ActionsheetDragIndicator />
          <AddPlayer
            tournament={tournament}
            teamId={team.id}
            onClose={handlePlayerAdded}
          />
        </ActionsheetContent>
      </Actionsheet>

      {/* Select Existing Player Modal */}
      <AsyncSelectWithSearch
        isOpen={showSelectPlayer}
        onClose={() => setShowSelectPlayer(false)}
        onSelect={handlePlayerSelect}
        selectedValues={[]}
        options={availablePlayers}
        hasMore={hasMorePlayers}
        loadMore={loadAvailablePlayers}
        onSearchChange={handleSearchChange}
        placeholder="Search for players..."
        multiple={true}
        maxSelect={10}
        renderOption={(option, isSelected) => (
          <VStack className="flex-1 py-2">
            <Text
              className={`text-base ${
                isSelected ? 'font-urbanistBold' : 'font-urbanistMedium'
              }`}
            >
              {option.label}
            </Text>
            {option.player?.jersey_number && (
              <Text
                className={`text-xs text-typography-600 ${
                  isSelected ? 'font-urbanistMedium' : 'font-urbanist'
                }`}
              >
                Jersey #{option.player.jersey_number}
              </Text>
            )}
          </VStack>
        )}
      />

      {loadingPlayers && (
        <View className="absolute inset-0 bg-black/20 items-center justify-center">
          <Spinner size="large" />
        </View>
      )}
    </View>
  );
};

export default SquadPage;
